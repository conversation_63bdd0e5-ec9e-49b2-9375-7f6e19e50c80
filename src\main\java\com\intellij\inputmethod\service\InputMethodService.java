package com.intellij.inputmethod.service;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.editor.colors.EditorColorsScheme;
import com.intellij.openapi.editor.markup.TextAttributes;
import com.intellij.inputmethod.settings.InputMethodSettings;
import com.intellij.inputmethod.util.ContextDetector;
import com.intellij.inputmethod.util.InputMethodType;
import com.intellij.inputmethod.util.SystemInputMethodManager;

import java.awt.*;

/**
 * Core service for managing input method switching and cursor color indication.
 * This service handles the main logic for detecting context and switching input methods.
 */
@Service(Service.Level.APP)
public final class InputMethodService {
    
    private static final Logger LOG = Logger.getInstance(InputMethodService.class);
    
    private final InputMethodSettings settings;
    private final SystemInputMethodManager systemInputMethodManager;
    private final ContextDetector contextDetector;
    
    private boolean isEnabled = true;
    private InputMethodType currentInputMethod = InputMethodType.ENGLISH;
    
    public InputMethodService() {
        this.settings = InputMethodSettings.getInstance();
        this.systemInputMethodManager = new SystemInputMethodManager();
        this.contextDetector = new ContextDetector();
        
        LOG.info("InputMethodService initialized");
    }
    
    public static InputMethodService getInstance() {
        return ApplicationManager.getApplication().getService(InputMethodService.class);
    }
    
    /**
     * Handle caret position change and potentially switch input method
     */
    public void handleCaretPositionChange(Editor editor, int offset) {
        if (!isEnabled || editor == null) {
            return;
        }
        
        try {
            // Detect context at current position
            InputMethodType requiredInputMethod = contextDetector.detectRequiredInputMethod(editor, offset);
            
            // Switch input method if needed
            if (requiredInputMethod != currentInputMethod) {
                switchInputMethod(requiredInputMethod);
            }
            
            // Update cursor color
            updateCursorColor(editor, requiredInputMethod);
            
        } catch (Exception e) {
            LOG.warn("Error handling caret position change", e);
        }
    }
    
    /**
     * Switch to the specified input method
     */
    public void switchInputMethod(InputMethodType inputMethodType) {
        if (inputMethodType == currentInputMethod) {
            return;
        }
        
        try {
            boolean success = systemInputMethodManager.switchTo(inputMethodType);
            if (success) {
                currentInputMethod = inputMethodType;
                LOG.debug("Switched to input method: " + inputMethodType);
            } else {
                LOG.warn("Failed to switch to input method: " + inputMethodType);
            }
        } catch (Exception e) {
            LOG.error("Error switching input method", e);
        }
    }
    
    /**
     * Update cursor color based on input method type
     */
    private void updateCursorColor(Editor editor, InputMethodType inputMethodType) {
        if (!settings.isVisualIndicationEnabled()) {
            return;
        }
        
        try {
            Color cursorColor = getCursorColorForInputMethod(inputMethodType);
            
            // Apply cursor color to editor
            EditorColorsScheme scheme = editor.getColorsScheme();
            TextAttributes caretAttributes = new TextAttributes();
            caretAttributes.setForegroundColor(cursorColor);
            
            // Note: This is a simplified approach. In a real implementation,
            // you might need to use editor markup or other mechanisms to change cursor color
            
        } catch (Exception e) {
            LOG.warn("Error updating cursor color", e);
        }
    }
    
    /**
     * Get cursor color for the specified input method type
     */
    private Color getCursorColorForInputMethod(InputMethodType inputMethodType) {
        switch (inputMethodType) {
            case ENGLISH:
                return settings.getEnglishCursorColor();
            case CHINESE:
                return settings.getChineseCursorColor();
            default:
                return Color.BLACK;
        }
    }
    
    /**
     * Enable or disable the input method switching
     */
    public void setEnabled(boolean enabled) {
        this.isEnabled = enabled;
        LOG.info("Input method switching " + (enabled ? "enabled" : "disabled"));
    }
    
    public boolean isEnabled() {
        return isEnabled;
    }
    
    public InputMethodType getCurrentInputMethod() {
        return currentInputMethod;
    }
    
    /**
     * Force switch to English input method
     */
    public void forceEnglish() {
        switchInputMethod(InputMethodType.ENGLISH);
    }
    
    /**
     * Force switch to Chinese input method
     */
    public void forceChinese() {
        switchInputMethod(InputMethodType.CHINESE);
    }
}
