package com.intellij.inputmethod.util;

import com.intellij.lang.Language;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.*;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.inputmethod.settings.InputMethodSettings;

/**
 * Detects the context at a given position in the editor and determines
 * the appropriate input method to use.
 */
public class ContextDetector {
    
    private static final Logger LOG = Logger.getInstance(ContextDetector.class);
    
    private final InputMethodSettings settings;
    
    public ContextDetector() {
        this.settings = InputMethodSettings.getInstance();
    }
    
    /**
     * Detect the required input method based on the context at the given offset.
     * 
     * @param editor The editor instance
     * @param offset The caret offset position
     * @return The required input method type
     */
    public InputMethodType detectRequiredInputMethod(Editor editor, int offset) {
        if (editor == null || editor.getProject() == null) {
            return InputMethodType.ENGLISH;
        }
        
        try {
            Project project = editor.getProject();
            Document document = editor.getDocument();
            
            // Check if file type is supported
            FileType fileType = getFileType(editor);
            if (!isSupportedFileType(fileType)) {
                return InputMethodType.ENGLISH;
            }
            
            // Get PSI file for advanced analysis
            PsiFile psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document);
            if (psiFile == null) {
                return detectBySimpleHeuristics(document, offset);
            }
            
            // Use PSI-based detection for more accurate results
            return detectByPsiAnalysis(psiFile, offset);
            
        } catch (Exception e) {
            LOG.warn("Error detecting context, falling back to English", e);
            return InputMethodType.ENGLISH;
        }
    }
    
    /**
     * Detect context using PSI (Program Structure Interface) analysis
     */
    private InputMethodType detectByPsiAnalysis(PsiFile psiFile, int offset) {
        PsiElement element = psiFile.findElementAt(offset);
        if (element == null) {
            return InputMethodType.ENGLISH;
        }
        
        // Check if we're in a comment
        if (isInComment(element)) {
            return settings.isSwitchInComments() ? InputMethodType.CHINESE : InputMethodType.ENGLISH;
        }
        
        // Check if we're in a string literal
        if (isInStringLiteral(element)) {
            return settings.isSwitchInStrings() ? detectStringContentType(element) : InputMethodType.ENGLISH;
        }
        
        // Check if we're in documentation
        if (isInDocumentation(element)) {
            return settings.isSwitchInDocumentation() ? InputMethodType.CHINESE : InputMethodType.ENGLISH;
        }
        
        // Default to English for code
        return InputMethodType.ENGLISH;
    }
    
    /**
     * Simple heuristic-based detection when PSI is not available
     */
    private InputMethodType detectBySimpleHeuristics(Document document, int offset) {
        try {
            int lineNumber = document.getLineNumber(offset);
            int lineStartOffset = document.getLineStartOffset(lineNumber);
            int lineEndOffset = document.getLineEndOffset(lineNumber);
            
            String lineText = document.getText(new TextRange(lineStartOffset, lineEndOffset));
            String trimmedLine = lineText.trim();
            
            // Simple comment detection
            if (trimmedLine.startsWith("//") || trimmedLine.startsWith("/*") || 
                trimmedLine.startsWith("*") || trimmedLine.startsWith("#")) {
                return settings.isSwitchInComments() ? InputMethodType.CHINESE : InputMethodType.ENGLISH;
            }
            
            // Simple string detection (very basic)
            int relativeOffset = offset - lineStartOffset;
            if (relativeOffset < lineText.length()) {
                char charBefore = relativeOffset > 0 ? lineText.charAt(relativeOffset - 1) : ' ';
                char charAfter = relativeOffset < lineText.length() - 1 ? lineText.charAt(relativeOffset + 1) : ' ';
                
                if (charBefore == '"' || charAfter == '"' || charBefore == '\'' || charAfter == '\'') {
                    return settings.isSwitchInStrings() ? InputMethodType.CHINESE : InputMethodType.ENGLISH;
                }
            }
            
        } catch (Exception e) {
            LOG.warn("Error in simple heuristic detection", e);
        }
        
        return InputMethodType.ENGLISH;
    }
    
    /**
     * Check if the element is within a comment
     */
    private boolean isInComment(PsiElement element) {
        PsiElement current = element;
        while (current != null) {
            if (current instanceof PsiComment) {
                return true;
            }
            current = current.getParent();
        }
        return false;
    }
    
    /**
     * Check if the element is within a string literal
     */
    private boolean isInStringLiteral(PsiElement element) {
        PsiElement current = element;
        while (current != null) {
            if (isStringLiteralElement(current)) {
                return true;
            }
            current = current.getParent();
        }
        return false;
    }
    
    /**
     * Check if the element is within documentation
     */
    private boolean isInDocumentation(PsiElement element) {
        PsiElement current = element;
        while (current != null) {
            if (current instanceof PsiDocComment || 
                (current instanceof PsiComment && isDocumentationComment(current))) {
                return true;
            }
            current = current.getParent();
        }
        return false;
    }
    
    /**
     * Detect the content type of a string literal
     */
    private InputMethodType detectStringContentType(PsiElement stringElement) {
        String text = stringElement.getText();
        if (text == null || text.length() < 2) {
            return InputMethodType.ENGLISH;
        }
        
        // Remove quotes
        String content = text.substring(1, text.length() - 1);
        
        // Simple heuristic: if contains Chinese characters, use Chinese input
        for (char c : content.toCharArray()) {
            if (isChineseCharacter(c)) {
                return InputMethodType.CHINESE;
            }
        }
        
        return InputMethodType.ENGLISH;
    }
    
    /**
     * Check if a character is a Chinese character
     */
    private boolean isChineseCharacter(char c) {
        return (c >= 0x4E00 && c <= 0x9FFF) || // CJK Unified Ideographs
               (c >= 0x3400 && c <= 0x4DBF) || // CJK Extension A
               (c >= 0x20000 && c <= 0x2A6DF) || // CJK Extension B
               (c >= 0x2A700 && c <= 0x2B73F) || // CJK Extension C
               (c >= 0x2B740 && c <= 0x2B81F) || // CJK Extension D
               (c >= 0x2B820 && c <= 0x2CEAF); // CJK Extension E
    }
    
    /**
     * Check if an element represents a string literal
     */
    private boolean isStringLiteralElement(PsiElement element) {
        // This is a simplified check - in a real implementation,
        // you'd want to check for language-specific string literal types
        String elementType = element.getNode() != null ? element.getNode().getElementType().toString() : "";
        return elementType.contains("STRING") || elementType.contains("LITERAL");
    }
    
    /**
     * Check if a comment is a documentation comment
     */
    private boolean isDocumentationComment(PsiElement comment) {
        String text = comment.getText();
        return text.startsWith("/**") || text.startsWith("///") || text.startsWith("##");
    }
    
    /**
     * Get the file type from the editor
     */
    private FileType getFileType(Editor editor) {
        if (editor.getVirtualFile() != null) {
            return editor.getVirtualFile().getFileType();
        }
        return null;
    }
    
    /**
     * Check if the file type is supported
     */
    private boolean isSupportedFileType(FileType fileType) {
        if (fileType == null) {
            return false;
        }
        
        String extension = fileType.getDefaultExtension();
        return settings.getSupportedFileExtensions().contains(extension);
    }
}
