package com.intellij.inputmethod.action;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.DumbAware;
import com.intellij.inputmethod.service.InputMethodService;
import org.jetbrains.annotations.NotNull;

/**
 * Action to manually switch to Chinese input method.
 */
public class SwitchToChineseAction extends AnAction implements DumbAware {
    
    private static final Logger LOG = Logger.getInstance(SwitchToChineseAction.class);
    
    @Override
    public void actionPerformed(@NotNull AnActionEvent event) {
        try {
            InputMethodService service = InputMethodService.getInstance();
            service.forceChinese();
            
            LOG.info("Manually switched to Chinese input method");
            
        } catch (Exception e) {
            LOG.error("Error switching to Chinese input method", e);
        }
    }
    
    @Override
    public void update(@NotNull AnActionEvent event) {
        // Always enable this action
        event.getPresentation().setEnabledAndVisible(true);
    }
}
