package com.intellij.inputmethod.listener;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.event.DocumentEvent;
import com.intellij.openapi.editor.event.DocumentListener;
import com.intellij.inputmethod.service.InputMethodService;
import org.jetbrains.annotations.NotNull;

/**
 * Listens to document changes that might affect input method switching decisions.
 */
public class DocumentChangeListener implements DocumentListener {
    
    private static final Logger LOG = Logger.getInstance(DocumentChangeListener.class);
    
    private final InputMethodService inputMethodService;
    
    public DocumentChangeListener() {
        this.inputMethodService = InputMethodService.getInstance();
    }
    
    @Override
    public void documentChanged(@NotNull DocumentEvent event) {
        try {
            Document document = event.getDocument();
            int offset = event.getOffset();
            
            if (LOG.isDebugEnabled()) {
                LOG.debug("Document changed at offset: " + offset + 
                         ", length: " + event.getOldLength() + 
                         " -> " + event.getNewLength());
            }
            
            // For now, we don't need to do anything special on document changes
            // The caret listener will handle position changes
            // This listener is here for future enhancements
            
        } catch (Exception e) {
            LOG.warn("Error handling document change", e);
        }
    }
    
    @Override
    public void beforeDocumentChange(@NotNull DocumentEvent event) {
        // Can be used for pre-processing if needed
    }
}
