# 智能输入法切换插件 (Smart Input Method Switcher)

专为中文开发者设计的JetBrains IDE智能输入法切换插件，解决编码过程中频繁切换输入法的痛点。

## 🚀 功能特性

### 核心功能
- **智能切换**: 根据光标位置自动切换中英文输入法
- **视觉指示**: 光标颜色实时显示当前输入法状态
- **上下文感知**: 识别代码、注释、字符串等不同区域
- **多语言支持**: 支持Java、Python、JavaScript等主流编程语言

### 使用场景
- **代码区域**: 自动切换到英文输入法（蓝色光标）
- **注释区域**: 自动切换到中文输入法（红色光标）
- **字符串内容**: 根据内容智能判断输入法类型
- **文档编辑**: 支持中英文混合输入

### 配置选项
- 启用/禁用自动切换功能
- 自定义光标颜色指示
- 配置不同区域的切换规则
- 支持的文件类型管理

## 🛠️ 开发环境

### 系统要求
- JDK 17 或更高版本
- IntelliJ IDEA 2023.2 或更高版本
- Gradle 8.2 或更高版本

### 支持的操作系统
- Windows 10/11
- macOS 10.15 或更高版本
- Linux (Ubuntu, CentOS, Fedora等)

## 📦 构建和安装

### 构建插件
```bash
# 克隆项目
git clone <repository-url>
cd smart-input-method-switcher

# 构建插件
./gradlew buildPlugin

# 运行测试
./gradlew test

# 在IDE中运行插件进行测试
./gradlew runIde
```

### 安装插件
1. 构建完成后，在 `build/distributions/` 目录下找到生成的插件文件
2. 在IntelliJ IDEA中，打开 `File > Settings > Plugins`
3. 点击齿轮图标，选择 `Install Plugin from Disk...`
4. 选择构建生成的插件文件进行安装
5. 重启IDE

## ⚙️ 配置说明

### 基本设置
在 `File > Settings > Editor > Smart Input Method Switcher` 中可以配置：

- **启用自动切换**: 开启/关闭智能输入法切换功能
- **视觉指示**: 开启/关闭光标颜色指示
- **光标颜色**: 自定义英文和中文输入法的光标颜色

### 上下文规则
- **注释区域**: 配置是否在注释中自动切换到中文输入法
- **字符串区域**: 配置是否在字符串中智能判断输入法
- **文档区域**: 配置是否在文档注释中自动切换到中文输入法

### 文件类型支持
默认支持的文件类型：
- Java (.java)
- Kotlin (.kt)
- Python (.py)
- JavaScript/TypeScript (.js, .ts)
- C/C++ (.c, .cpp, .h)
- 其他常见编程语言文件

## 🎯 快捷键

- `Ctrl+Alt+I`: 切换智能输入法功能开关
- `Ctrl+Alt+E`: 手动切换到英文输入法
- `Ctrl+Alt+C`: 手动切换到中文输入法

## 🔧 技术架构

### 核心组件
- **InputMethodService**: 主要服务类，处理输入法切换逻辑
- **ContextDetector**: 上下文检测器，识别代码、注释、字符串等区域
- **SystemInputMethodManager**: 系统输入法管理器，处理平台特定的输入法切换
- **CaretPositionListener**: 光标位置监听器，响应光标移动事件

### 扩展点
- 编辑器光标监听器
- 文档变更监听器
- 应用程序配置界面
- 自定义操作和快捷键

## 🐛 问题排查

### 常见问题
1. **输入法切换不生效**
   - 检查操作系统是否支持程序化输入法切换
   - 确认已安装相应的中英文输入法
   - 查看IDE日志中的错误信息

2. **光标颜色不变化**
   - 确认已启用视觉指示功能
   - 检查编辑器主题是否支持自定义光标颜色
   - 尝试重启IDE

3. **某些文件类型不支持**
   - 在设置中添加相应的文件扩展名
   - 确认文件类型被IDE正确识别

### 调试模式
在设置中启用调试模式，可以在IDE日志中查看详细的运行信息。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个插件！

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

### 代码规范
- 遵循Java编码规范
- 添加适当的注释和文档
- 编写单元测试
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

如有问题或建议，请通过GitHub Issues联系我们。
