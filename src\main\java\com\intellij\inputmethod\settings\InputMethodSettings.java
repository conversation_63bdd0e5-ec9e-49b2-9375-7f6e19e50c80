package com.intellij.inputmethod.settings;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.PersistentStateComponent;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.components.State;
import com.intellij.openapi.components.Storage;
import com.intellij.util.xmlb.XmlSerializerUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.awt.*;
import java.util.HashSet;
import java.util.Set;

/**
 * Settings for the Smart Input Method Switcher plugin.
 * Stores user preferences and configuration options.
 */
@Service(Service.Level.APP)
@State(
    name = "InputMethodSettings",
    storages = @Storage("SmartInputMethodSwitcher.xml")
)
public final class InputMethodSettings implements PersistentStateComponent<InputMethodSettings> {
    
    // General settings
    public boolean enabled = true;
    public boolean visualIndicationEnabled = true;
    public boolean autoSwitchEnabled = true;
    
    // Cursor colors (stored as RGB values)
    public int englishCursorColorRGB = Color.BLUE.getRGB();
    public int chineseCursorColorRGB = Color.RED.getRGB();
    
    // Context detection settings
    public boolean switchInComments = true;
    public boolean switchInStrings = true;
    public boolean switchInDocumentation = true;
    
    // File type settings
    public Set<String> supportedFileExtensions = new HashSet<>();
    
    // Input method identifiers (platform-specific)
    public String englishInputMethodId = "";
    public String chineseInputMethodId = "";
    
    // Advanced settings
    public int switchDelayMs = 100;
    public boolean debugMode = false;
    
    public InputMethodSettings() {
        // Initialize default supported file extensions
        supportedFileExtensions.add("java");
        supportedFileExtensions.add("kt");
        supportedFileExtensions.add("py");
        supportedFileExtensions.add("js");
        supportedFileExtensions.add("ts");
        supportedFileExtensions.add("cpp");
        supportedFileExtensions.add("c");
        supportedFileExtensions.add("h");
        supportedFileExtensions.add("cs");
        supportedFileExtensions.add("go");
        supportedFileExtensions.add("rs");
        supportedFileExtensions.add("php");
        supportedFileExtensions.add("rb");
        supportedFileExtensions.add("swift");
        supportedFileExtensions.add("scala");
        supportedFileExtensions.add("xml");
        supportedFileExtensions.add("html");
        supportedFileExtensions.add("css");
        supportedFileExtensions.add("json");
        supportedFileExtensions.add("yaml");
        supportedFileExtensions.add("yml");
        supportedFileExtensions.add("md");
        supportedFileExtensions.add("txt");
    }
    
    public static InputMethodSettings getInstance() {
        return ApplicationManager.getApplication().getService(InputMethodSettings.class);
    }
    
    @Override
    public @Nullable InputMethodSettings getState() {
        return this;
    }
    
    @Override
    public void loadState(@NotNull InputMethodSettings state) {
        XmlSerializerUtil.copyBean(state, this);
    }
    
    // Getters and setters
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public boolean isVisualIndicationEnabled() {
        return visualIndicationEnabled;
    }
    
    public void setVisualIndicationEnabled(boolean visualIndicationEnabled) {
        this.visualIndicationEnabled = visualIndicationEnabled;
    }
    
    public boolean isAutoSwitchEnabled() {
        return autoSwitchEnabled;
    }
    
    public void setAutoSwitchEnabled(boolean autoSwitchEnabled) {
        this.autoSwitchEnabled = autoSwitchEnabled;
    }
    
    public Color getEnglishCursorColor() {
        return new Color(englishCursorColorRGB);
    }
    
    public void setEnglishCursorColor(Color color) {
        this.englishCursorColorRGB = color.getRGB();
    }
    
    public Color getChineseCursorColor() {
        return new Color(chineseCursorColorRGB);
    }
    
    public void setChineseCursorColor(Color color) {
        this.chineseCursorColorRGB = color.getRGB();
    }
    
    public boolean isSwitchInComments() {
        return switchInComments;
    }
    
    public void setSwitchInComments(boolean switchInComments) {
        this.switchInComments = switchInComments;
    }
    
    public boolean isSwitchInStrings() {
        return switchInStrings;
    }
    
    public void setSwitchInStrings(boolean switchInStrings) {
        this.switchInStrings = switchInStrings;
    }
    
    public boolean isSwitchInDocumentation() {
        return switchInDocumentation;
    }
    
    public void setSwitchInDocumentation(boolean switchInDocumentation) {
        this.switchInDocumentation = switchInDocumentation;
    }
    
    public Set<String> getSupportedFileExtensions() {
        return supportedFileExtensions;
    }
    
    public void setSupportedFileExtensions(Set<String> supportedFileExtensions) {
        this.supportedFileExtensions = supportedFileExtensions;
    }
    
    public String getEnglishInputMethodId() {
        return englishInputMethodId;
    }
    
    public void setEnglishInputMethodId(String englishInputMethodId) {
        this.englishInputMethodId = englishInputMethodId;
    }
    
    public String getChineseInputMethodId() {
        return chineseInputMethodId;
    }
    
    public void setChineseInputMethodId(String chineseInputMethodId) {
        this.chineseInputMethodId = chineseInputMethodId;
    }
    
    public int getSwitchDelayMs() {
        return switchDelayMs;
    }
    
    public void setSwitchDelayMs(int switchDelayMs) {
        this.switchDelayMs = switchDelayMs;
    }
    
    public boolean isDebugMode() {
        return debugMode;
    }
    
    public void setDebugMode(boolean debugMode) {
        this.debugMode = debugMode;
    }
}
