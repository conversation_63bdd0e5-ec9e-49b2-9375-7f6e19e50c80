plugins {
    id("java")
}

group = "com.intellij.inputmethod"
version = "1.0.0"

repositories {
    mavenCentral()
}

dependencies {
    testImplementation("junit:junit:4.13.2")
    testImplementation("org.mockito:mockito-core:5.5.0")
    
    // Add minimal IntelliJ dependencies for compilation
    compileOnly("org.jetbrains:annotations:24.0.1")
}

tasks {
    withType<JavaCompile> {
        sourceCompatibility = "17"
        targetCompatibility = "17"
        options.encoding = "UTF-8"
    }

    test {
        useJUnit()
    }
}
