<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>com.intellij.inputmethod.SmartInputMethodSwitcher</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>Smart Input Method Switcher</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor email="<EMAIL>" url="https://www.example.com">Smart Input Method Team</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Simple HTML elements (text formatting, paragraphs, and lists) can be added inside of <![CDATA[ ]]> tag.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
    <h2>智能输入法切换插件</h2>
    <p>专为中文开发者设计的JetBrains IDE智能输入法切换插件，解决编码过程中频繁切换输入法的痛点。</p>
    
    <h3>🚀 核心功能</h3>
    <ul>
        <li><strong>智能切换</strong>: 根据光标位置自动切换中英文输入法</li>
        <li><strong>视觉指示</strong>: 光标颜色实时显示当前输入法状态</li>
        <li><strong>上下文感知</strong>: 识别代码、注释、字符串等不同区域</li>
        <li><strong>多语言支持</strong>: 支持Java、Python、JavaScript等主流编程语言</li>
    </ul>
    
    <h3>🎯 使用场景</h3>
    <ul>
        <li><strong>代码区域</strong>: 自动切换到英文输入法（蓝色光标）</li>
        <li><strong>注释区域</strong>: 自动切换到中文输入法（红色光标）</li>
        <li><strong>字符串内容</strong>: 根据内容智能判断输入法类型</li>
        <li><strong>文档编辑</strong>: 支持中英文混合输入</li>
    </ul>
    
    <h3>⚙ 配置选项</h3>
    <ul>
        <li>启用/禁用自动切换功能</li>
        <li>自定义光标颜色指示</li>
        <li>配置不同区域的切换规则</li>
        <li>支持的文件类型管理</li>
    </ul>
    ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.modules.lang</depends>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- Application-level settings -->
        <applicationConfigurable 
            parentId="editor" 
            instance="com.intellij.inputmethod.settings.InputMethodConfigurable"
            id="com.intellij.inputmethod.settings.InputMethodConfigurable"
            displayName="Smart Input Method Switcher"/>
        
        <!-- Application service for managing input method switching -->
        <applicationService 
            serviceImplementation="com.intellij.inputmethod.service.InputMethodService"/>
        
        <!-- Editor caret listener for position changes -->
        <editorCaretListener 
            implementation="com.intellij.inputmethod.listener.CaretPositionListener"/>
        
        <!-- Document listener for content changes -->
        <documentListener 
            implementation="com.intellij.inputmethod.listener.DocumentChangeListener"/>
    </extensions>

    <!-- Actions -->
    <actions>
        <!-- Toggle input method switching -->
        <action id="com.intellij.inputmethod.ToggleInputMethodSwitching"
                class="com.intellij.inputmethod.action.ToggleInputMethodAction"
                text="Toggle Smart Input Method Switching"
                description="Enable or disable smart input method switching">
            <add-to-group group-id="EditMenu" anchor="last"/>
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt I"/>
        </action>
        
        <!-- Manual switch to English -->
        <action id="com.intellij.inputmethod.SwitchToEnglish"
                class="com.intellij.inputmethod.action.SwitchToEnglishAction"
                text="Switch to English Input"
                description="Manually switch to English input method">
            <add-to-group group-id="EditMenu" anchor="last"/>
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt E"/>
        </action>
        
        <!-- Manual switch to Chinese -->
        <action id="com.intellij.inputmethod.SwitchToChinese"
                class="com.intellij.inputmethod.action.SwitchToChineseAction"
                text="Switch to Chinese Input"
                description="Manually switch to Chinese input method">
            <add-to-group group-id="EditMenu" anchor="last"/>
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt C"/>
        </action>
    </actions>

    <!-- Application listeners -->
    <applicationListeners>
        <listener class="com.intellij.inputmethod.listener.ApplicationStartupListener"
                  topic="com.intellij.ide.AppLifecycleListener"/>
    </applicationListeners>
</idea-plugin>
