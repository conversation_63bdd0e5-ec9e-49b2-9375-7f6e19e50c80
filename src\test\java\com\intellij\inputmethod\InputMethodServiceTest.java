package com.intellij.inputmethod;

import com.intellij.inputmethod.util.InputMethodType;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Basic tests for the Smart Input Method Switcher plugin.
 */
public class InputMethodServiceTest {
    
    @Test
    public void testInputMethodTypeEnum() {
        // Test that our enum values are correctly defined
        assertEquals("ENGLISH", InputMethodType.ENGLISH.name());
        assertEquals("CHINESE", InputMethodType.CHINESE.name());
        assertEquals("UNKNOWN", InputMethodType.UNKNOWN.name());
    }
    
    @Test
    public void testInputMethodTypeValues() {
        // Test that we have the expected number of enum values
        InputMethodType[] values = InputMethodType.values();
        assertEquals(3, values.length);
        
        // Test that all expected values are present
        boolean hasEnglish = false;
        boolean hasChinese = false;
        boolean hasUnknown = false;
        
        for (InputMethodType type : values) {
            switch (type) {
                case ENGLISH:
                    hasEnglish = true;
                    break;
                case CHINESE:
                    hasChinese = true;
                    break;
                case UNKNOWN:
                    hasUnknown = true;
                    break;
            }
        }
        
        assertTrue("Should have ENGLISH type", hasEnglish);
        assertTrue("Should have CHINESE type", hasChinese);
        assertTrue("Should have UNKNOWN type", hasUnknown);
    }
}
