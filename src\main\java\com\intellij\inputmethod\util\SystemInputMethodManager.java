package com.intellij.inputmethod.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.util.SystemInfo;
import com.intellij.inputmethod.settings.InputMethodSettings;

import java.awt.im.InputContext;
import java.io.IOException;
import java.util.Locale;

/**
 * Manages system-level input method switching.
 * Provides platform-specific implementations for Windows, macOS, and Linux.
 */
public class SystemInputMethodManager {
    
    private static final Logger LOG = Logger.getInstance(SystemInputMethodManager.class);
    
    private final InputMethodSettings settings;
    
    public SystemInputMethodManager() {
        this.settings = InputMethodSettings.getInstance();
    }
    
    /**
     * Switch to the specified input method type
     * 
     * @param inputMethodType The target input method type
     * @return true if the switch was successful, false otherwise
     */
    public boolean switchTo(InputMethodType inputMethodType) {
        try {
            if (SystemInfo.isWindows) {
                return switchOnWindows(inputMethodType);
            } else if (SystemInfo.isMac) {
                return switchOnMacOS(inputMethodType);
            } else if (SystemInfo.isLinux) {
                return switchOnLinux(inputMethodType);
            } else {
                LOG.warn("Unsupported operating system for input method switching");
                return false;
            }
        } catch (Exception e) {
            LOG.error("Error switching input method", e);
            return false;
        }
    }
    
    /**
     * Switch input method on Windows
     */
    private boolean switchOnWindows(InputMethodType inputMethodType) {
        try {
            String command;
            switch (inputMethodType) {
                case ENGLISH:
                    // Switch to English (US) keyboard layout
                    command = "powershell.exe -Command \"Set-WinUserLanguageList -LanguageList en-US -Force\"";
                    break;
                case CHINESE:
                    // Switch to Chinese input method
                    command = "powershell.exe -Command \"Set-WinUserLanguageList -LanguageList zh-CN -Force\"";
                    break;
                default:
                    return false;
            }
            
            Process process = Runtime.getRuntime().exec(command);
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                LOG.debug("Successfully switched to " + inputMethodType + " on Windows");
                return true;
            } else {
                LOG.warn("Failed to switch input method on Windows, exit code: " + exitCode);
                return false;
            }
            
        } catch (IOException | InterruptedException e) {
            LOG.error("Error executing Windows input method switch command", e);
            return false;
        }
    }
    
    /**
     * Switch input method on macOS
     */
    private boolean switchOnMacOS(InputMethodType inputMethodType) {
        try {
            String inputSource;
            switch (inputMethodType) {
                case ENGLISH:
                    inputSource = "com.apple.keylayout.US";
                    break;
                case CHINESE:
                    inputSource = "com.apple.inputmethod.SCIM.ITABC"; // Simplified Chinese
                    break;
                default:
                    return false;
            }
            
            // Use AppleScript to switch input method
            String[] command = {
                "osascript", "-e",
                "tell application \"System Events\" to tell process \"SystemUIServer\" to " +
                "click (menu bar item 1 of menu bar 1 whose description contains \"text input\")"
            };
            
            Process process = Runtime.getRuntime().exec(command);
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                LOG.debug("Successfully switched to " + inputMethodType + " on macOS");
                return true;
            } else {
                LOG.warn("Failed to switch input method on macOS, exit code: " + exitCode);
                return false;
            }
            
        } catch (IOException | InterruptedException e) {
            LOG.error("Error executing macOS input method switch command", e);
            return false;
        }
    }
    
    /**
     * Switch input method on Linux
     */
    private boolean switchOnLinux(InputMethodType inputMethodType) {
        try {
            // Try different Linux input method frameworks
            return tryIBus(inputMethodType) || 
                   tryFcitx(inputMethodType) || 
                   tryScim(inputMethodType);
                   
        } catch (Exception e) {
            LOG.error("Error switching input method on Linux", e);
            return false;
        }
    }
    
    /**
     * Try switching using IBus
     */
    private boolean tryIBus(InputMethodType inputMethodType) {
        try {
            String engine;
            switch (inputMethodType) {
                case ENGLISH:
                    engine = "xkb:us::eng";
                    break;
                case CHINESE:
                    engine = "pinyin"; // or "libpinyin", "sunpinyin", etc.
                    break;
                default:
                    return false;
            }
            
            String[] command = {"ibus", "engine", engine};
            Process process = Runtime.getRuntime().exec(command);
            int exitCode = process.waitFor();
            
            return exitCode == 0;
            
        } catch (IOException | InterruptedException e) {
            LOG.debug("IBus not available or failed", e);
            return false;
        }
    }
    
    /**
     * Try switching using Fcitx
     */
    private boolean tryFcitx(InputMethodType inputMethodType) {
        try {
            String method;
            switch (inputMethodType) {
                case ENGLISH:
                    method = "keyboard-us";
                    break;
                case CHINESE:
                    method = "pinyin";
                    break;
                default:
                    return false;
            }
            
            String[] command = {"fcitx-remote", "-s", method};
            Process process = Runtime.getRuntime().exec(command);
            int exitCode = process.waitFor();
            
            return exitCode == 0;
            
        } catch (IOException | InterruptedException e) {
            LOG.debug("Fcitx not available or failed", e);
            return false;
        }
    }
    
    /**
     * Try switching using SCIM
     */
    private boolean tryScim(InputMethodType inputMethodType) {
        try {
            // SCIM switching is more complex and may require different approaches
            // This is a placeholder implementation
            LOG.debug("SCIM input method switching not fully implemented");
            return false;
            
        } catch (Exception e) {
            LOG.debug("SCIM not available or failed", e);
            return false;
        }
    }
    
    /**
     * Get the current system input method (if possible)
     */
    public InputMethodType getCurrentInputMethod() {
        try {
            InputContext inputContext = InputContext.getInstance();
            if (inputContext != null) {
                Locale locale = inputContext.getLocale();
                if (locale != null) {
                    String language = locale.getLanguage();
                    if ("zh".equals(language)) {
                        return InputMethodType.CHINESE;
                    } else if ("en".equals(language)) {
                        return InputMethodType.ENGLISH;
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Error getting current input method", e);
        }
        
        return InputMethodType.UNKNOWN;
    }
    
    /**
     * Check if input method switching is supported on this platform
     */
    public boolean isSupported() {
        return SystemInfo.isWindows || SystemInfo.isMac || SystemInfo.isLinux;
    }
}
