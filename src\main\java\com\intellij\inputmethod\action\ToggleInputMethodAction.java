package com.intellij.inputmethod.action;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.DumbAware;
import com.intellij.inputmethod.service.InputMethodService;
import org.jetbrains.annotations.NotNull;

/**
 * Action to toggle the smart input method switching feature on/off.
 */
public class ToggleInputMethodAction extends AnAction implements DumbAware {
    
    private static final Logger LOG = Logger.getInstance(ToggleInputMethodAction.class);
    
    @Override
    public void actionPerformed(@NotNull AnActionEvent event) {
        try {
            InputMethodService service = InputMethodService.getInstance();
            boolean currentState = service.isEnabled();
            service.setEnabled(!currentState);
            
            String message = currentState ? "disabled" : "enabled";
            LOG.info("Smart input method switching " + message);
            
            // Update the action presentation
            updatePresentation(event, !currentState);
            
        } catch (Exception e) {
            LOG.error("Error toggling input method switching", e);
        }
    }
    
    @Override
    public void update(@NotNull AnActionEvent event) {
        try {
            InputMethodService service = InputMethodService.getInstance();
            boolean isEnabled = service.isEnabled();
            updatePresentation(event, isEnabled);
            
        } catch (Exception e) {
            LOG.warn("Error updating toggle action presentation", e);
        }
    }
    
    private void updatePresentation(AnActionEvent event, boolean isEnabled) {
        String text = isEnabled ? 
            "Disable Smart Input Method Switching" : 
            "Enable Smart Input Method Switching";
        
        String description = isEnabled ?
            "Disable automatic input method switching based on context" :
            "Enable automatic input method switching based on context";
            
        event.getPresentation().setText(text);
        event.getPresentation().setDescription(description);
    }
}
