package com.intellij.inputmethod.listener;

import com.intellij.ide.AppLifecycleListener;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.inputmethod.service.InputMethodService;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * Handles application lifecycle events for the Smart Input Method Switcher plugin.
 */
public class ApplicationStartupListener implements AppLifecycleListener {
    
    private static final Logger LOG = Logger.getInstance(ApplicationStartupListener.class);
    
    @Override
    public void appFrameCreated(@NotNull List<String> commandLineArgs) {
        try {
            LOG.info("Smart Input Method Switcher plugin initializing...");
            
            // Initialize the input method service
            InputMethodService inputMethodService = InputMethodService.getInstance();
            
            LOG.info("Smart Input Method Switcher plugin initialized successfully");
            
        } catch (Exception e) {
            LOG.error("Error initializing Smart Input Method Switcher plugin", e);
        }
    }
    
    @Override
    public void appClosing() {
        try {
            LOG.info("Smart Input Method Switcher plugin shutting down...");
            
            // Perform any cleanup if needed
            
            LOG.info("Smart Input Method Switcher plugin shutdown complete");
            
        } catch (Exception e) {
            LOG.error("Error during Smart Input Method Switcher plugin shutdown", e);
        }
    }
}
