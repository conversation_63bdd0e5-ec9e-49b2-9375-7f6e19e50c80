package com.intellij.inputmethod.listener;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.event.CaretEvent;
import com.intellij.openapi.editor.event.CaretListener;
import com.intellij.inputmethod.service.InputMethodService;
import org.jetbrains.annotations.NotNull;

/**
 * Listens to caret position changes and triggers input method switching logic.
 */
public class CaretPositionListener implements CaretListener {
    
    private static final Logger LOG = Logger.getInstance(CaretPositionListener.class);
    
    private final InputMethodService inputMethodService;
    
    public CaretPositionListener() {
        this.inputMethodService = InputMethodService.getInstance();
    }
    
    @Override
    public void caretPositionChanged(@NotNull CaretEvent event) {
        try {
            Editor editor = event.getEditor();
            int offset = event.getNewPosition().offset;
            
            if (LOG.isDebugEnabled()) {
                LOG.debug("Caret position changed to offset: " + offset + " in editor: " + editor.getVirtualFile());
            }
            
            // Delegate to the input method service
            inputMethodService.handleCaretPositionChange(editor, offset);
            
        } catch (Exception e) {
            LOG.warn("Error handling caret position change", e);
        }
    }
    
    @Override
    public void caretAdded(@NotNull CaretEvent event) {
        // Handle multiple carets if needed
        caretPositionChanged(event);
    }
    
    @Override
    public void caretRemoved(@NotNull CaretEvent event) {
        // Handle caret removal if needed
        if (LOG.isDebugEnabled()) {
            LOG.debug("Caret removed from editor: " + event.getEditor().getVirtualFile());
        }
    }
}
