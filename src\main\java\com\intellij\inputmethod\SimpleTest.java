package com.intellij.inputmethod;

import com.intellij.inputmethod.util.InputMethodType;

/**
 * Simple test class to verify basic compilation
 */
public class SimpleTest {
    
    public static void main(String[] args) {
        System.out.println("Smart Input Method Switcher Plugin");
        
        // Test enum
        for (InputMethodType type : InputMethodType.values()) {
            System.out.println("Input method type: " + type);
        }
        
        System.out.println("Basic compilation test passed!");
    }
    
    public InputMethodType getDefaultInputMethod() {
        return InputMethodType.ENGLISH;
    }
}
